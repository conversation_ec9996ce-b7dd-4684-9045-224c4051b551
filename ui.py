my code : #!/usr/bin/env python3
"""
🐺 Wild Weasel UI Automation - Complete CSV-Based Gainsight Activity Creation
=============================================================================
Mission: Create activities via UI automation from CSV data with complete field mapping
Built to handle all CSV fields with proper date conversion and null value handling
"""

import csv
import json
import os
import sys
import time
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/wild_weasel_ui_complete.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WildWeasel-UI-Complete")

class WildWeaselCompleteUIAutomation:
    """Complete Wild Weasel UI automation for CSV-based Gainsight activities"""
    
    def __init__(self):
        self.config = {
            "gainsight_url": "https://demo-emea1.gainsightcloud.com",
            "login_url": "https://demo-emea1.gainsightcloud.com/v1/ui/home",
            "target_c360_url": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca",
            
            # CSV source file
            "csv_file": "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped_enhanced_demo.csv",
            
            # Output files
            "success_log": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/ui_migration_success_complete.json",
            "failed_log": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/ui_migration_failed_complete.json"
        }
        
        self.activities = []
        self.migration_results = {
            "successful": [],
            "failed": [],
            "total_processed": 0,
            "start_time": None,
            "end_time": None
        }
        
        # Embedded credentials
        self.credentials = {
            "username": "<EMAIL>",
            "password": "@Ramprasad826ie"
        }
    
    def mission_brief(self):
        """Display the UI Automation mission brief"""
        print("🐺" + "="*100)
        print("  WILD WEASEL COMPLETE UI AUTOMATION - CSV-BASED ACTIVITY CREATION")
        print("="*102)
        print("🎯 COMPLETE UI AUTOMATION FEATURES:")
        print("  ✅ CSV DATA PROCESSING:")
        print("     → Full CSV field mapping and processing")
        print("     → Date format conversion (2025-05-29 03:30:19 → 5/29/2025 & 03:30)")
        print("     → Null value handling (skip blank fields)")
        print("  ✅ COMPREHENSIVE FORM FILLING:")
        print("     → Activity Type dropdown selection")
        print("     → Subject field population")
        print("     → Activity Date with proper format")
        print("     → Time field extraction and formatting")
        print("     → Notes/Plain Text content")
        print("     → Internal Recipients (Author Name)")
        print("     → Touchpoint Reason dropdown")
        print("     → Flow Type dropdown")
        print("  ✅ SMART UI INTERACTION:")
        print("     → Multiple selector strategies for robustness")
        print("     → Scrolling and waiting for elements")
        print("     → Error handling with screenshots")
        print("="*102)
        print(f"📁 CSV Source: {self.config['csv_file']}")
        print(f"🎯 Target C360: {self.config['target_c360_url']}")
        print(f"👤 Username: {self.credentials['username']}")
        print("="*102)
    
    def load_csv_activities(self):
        """Load activities from CSV file"""
        try:
            if not os.path.exists(self.config['csv_file']):
                logger.error(f"❌ CSV file not found: {self.config['csv_file']}")
                return False
            
            self.activities = []
            with open(self.config['csv_file'], 'r', encoding='utf-8') as f:
                csv_reader = csv.DictReader(f)
                for row in csv_reader:
                    activity = {
                        "row_number": row.get('Row Number', ''),
                        "subject": row.get('Subject', '').strip(),
                        "activity_date": row.get('Activity Date', '').strip(),
                        "activity_type": row.get('Activity Type', '').strip(),
                        "content_html": row.get('Content (HTML)', '').strip(),
                        "plain_text": row.get('Plain Text', '').strip(),
                        "author_name": row.get('Author Name', '').strip(),
                        "author_email": row.get('Author Email', '').strip(),
                        "flow_type": row.get('Flow Type', '').strip(),
                        "touchpoint_reason": row.get('Touchpoint Reason', '').strip(),
                        "external_attendees": row.get('External Attendees', '').strip(),
                        "company": row.get('Company', '').strip(),
                        "original_activity_type": row.get('Original Activity Type', '').strip(),
                        "original_meeting_type": row.get('Original Meeting Type', '').strip(),
                        "source": row.get('Source', '').strip()
                    }
                    self.activities.append(activity)
            
            logger.info(f"📊 Loaded {len(self.activities)} activities from CSV for UI automation")
            return True
                
        except Exception as e:
            logger.error(f"❌ Failed to load CSV activities: {e}")
            return False
    
    def convert_date_format(self, date_string):
        """Convert date from '2025-05-29 03:30:19' to '5/29/2025' and '03:30'"""
        try:
            if not date_string or date_string.strip() == '':
                return None, None
            
            # Parse the datetime string
            dt = datetime.strptime(date_string.strip(), '%Y-%m-%d %H:%M:%S')
            
            # Format for Gainsight: M/D/YYYY
            gainsight_date = f"{dt.month}/{dt.day}/{dt.year}"
            
            # Format time as HH:MM
            gainsight_time = f"{dt.hour:02d}:{dt.minute:02d}"
            
            return gainsight_date, gainsight_time
            
        except Exception as e:
            logger.error(f"❌ Date conversion failed for '{date_string}': {e}")
            return None, None
    
    def login_to_gainsight(self, page):
        """Login to Gainsight"""
        try:
            logger.info("🔐 Logging into Gainsight...")
            
            page.goto(self.config["login_url"])
            page.wait_for_load_state("networkidle", timeout=15000)
            
            # Fill credentials
            page.fill("input[name='username']", self.credentials["username"])
            page.fill("input[name='password'], input[type='password']", self.credentials["password"])
            page.click("button:has-text('Log In'), button[type='submit']")
            
            # Wait for successful login
            page.wait_for_function(
                "() => window.location.href.includes('/home') || window.location.href.includes('/dashboard')",
                timeout=30000
            )
            
            logger.info("✅ Successfully logged into Gainsight!")
            return True
                
        except Exception as e:
            logger.error(f"❌ Login failed: {e}")
            page.screenshot(path="/Users/<USER>/Desktop/wild_weasel_gainsight_migration/login_error_complete.png")
            return False
    
    def navigate_to_timeline(self, page):
        """Navigate to C360 Timeline"""
        try:
            logger.info("🎯 Navigating to C360 Timeline...")
            
            page.goto(self.config["target_c360_url"])
            page.wait_for_load_state("networkidle", timeout=30000)
            time.sleep(3)
            
            # Find and click Timeline tab
            timeline_selectors = [
                'a:has-text("Timeline")',
                'li:has-text("Timeline")',
                'div[role="tab"]:has-text("Timeline")',
                'button:has-text("Timeline")',
                '[data-testid*="timeline"]'
            ]
            
            timeline_found = False
            for selector in timeline_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.click(selector)
                        time.sleep(3)
                        timeline_found = True
                        logger.info(f"📌 Timeline clicked: {selector}")
                        break
                except:
                    continue
            
            if not timeline_found:
                # JavaScript fallback
                timeline_clicked = page.evaluate("""() => {
                    const elements = document.querySelectorAll('*');
                    for (const el of elements) {
                        if (el.innerText && el.innerText.trim().toLowerCase() === 'timeline' && el.offsetParent !== null) {
                            el.click();
                            return true;
                        }
                    }
                    return false;
                }""")
                
                if timeline_clicked:
                    logger.info("📌 Timeline clicked via JavaScript")
                    time.sleep(3)
                    timeline_found = True
            
            if timeline_found:
                page.screenshot(path="/Users/<USER>/Desktop/wild_weasel_gainsight_migration/timeline_complete.png")
                logger.info("✅ Successfully navigated to Timeline")
                return True
            else:
                logger.error("❌ Failed to find Timeline tab")
                return False
                
        except Exception as e:
            logger.error(f"❌ Timeline navigation failed: {e}")
            return False
    
    def wait_for_element_and_click(self, page, selectors, element_name, timeout=10000):
        """Wait for element using multiple selectors and click it"""
        for selector in selectors:
            try:
                element = page.locator(selector).first
                element.wait_for(state="visible", timeout=timeout)
                element.click()
                logger.info(f"✅ {element_name} clicked using selector: {selector}")
                return True
            except:
                continue
        
        # JavaScript fallback
        try:
            result = page.evaluate(f"""() => {{
                const buttons = document.querySelectorAll('button, a, div, span');
                for (const btn of buttons) {{
                    const text = btn.innerText || btn.textContent || '';
                    if (text.toLowerCase().includes('{element_name.lower()}') && btn.offsetParent !== null) {{
                        btn.click();
                        return true;
                    }}
                }}
                return false;
            }}""")
            
            if result:
                logger.info(f"✅ {element_name} clicked via JavaScript")
                return True
        except:
            pass
        
        logger.error(f"❌ Failed to click {element_name}")
        return False
    
    def fill_field_with_fallback(self, page, selectors, value, field_name):
        """Fill field using multiple selectors with fallback"""
        if not value or value.strip() == '':
            logger.info(f"⏭️ Skipping {field_name} - value is empty")
            return True
        
        for selector in selectors:
            try:
                element = page.locator(selector).first
                element.wait_for(state="visible", timeout=5000)
                element.fill(value)
                logger.info(f"✅ {field_name} filled using selector: {selector}")
                return True
            except:
                continue
        
        # JavaScript fallback
        try:
            result = page.evaluate(f"""(value) => {{
                const inputs = document.querySelectorAll('input, textarea');
                for (const input of inputs) {{
                    const placeholder = input.placeholder || '';
                    const name = input.name || '';
                    if ((placeholder.toLowerCase().includes('{field_name.lower()}') || 
                         name.toLowerCase().includes('{field_name.lower()}')) && 
                        input.offsetParent !== null) {{
                        input.value = value;
                        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        return true;
                    }}
                }}
                return false;
            }}""", value)
            
            if result:
                logger.info(f"✅ {field_name} filled via JavaScript")
                return True
        except:
            pass
        
        logger.error(f"❌ Failed to fill {field_name}")
        return False
    
    def select_dropdown_option(self, page, dropdown_selectors, option_value, field_name):
        """Select dropdown option with multiple strategies"""
        if not option_value or option_value.strip() == '':
            logger.info(f"⏭️ Skipping {field_name} dropdown - value is empty")
            return True
        
        try:
            # First, click to open the dropdown
            dropdown_opened = False
            for selector in dropdown_selectors:
                try:
                    element = page.locator(selector).first
                    element.wait_for(state="visible", timeout=5000)
                    element.click()
                    time.sleep(1)
                    dropdown_opened = True
                    logger.info(f"📋 {field_name} dropdown opened using: {selector}")
                    break
                except:
                    continue
            
            if not dropdown_opened:
                logger.error(f"❌ Failed to open {field_name} dropdown")
                return False
            
            # Wait for dropdown options to appear
            time.sleep(2)
            
            # Try to select the option
            option_selectors = [
                f'nz-option:has-text("{option_value}")',
                f'li:has-text("{option_value}")',
                f'.ant-select-dropdown-menu-item:has-text("{option_value}")',
                f'[role="option"]:has-text("{option_value}")',
                f'div:has-text("{option_value}")'
            ]
            
            for selector in option_selectors:
                try:
                    option_element = page.locator(selector).first
                    option_element.wait_for(state="visible", timeout=5000)
                    option_element.click()
                    logger.info(f"✅ {field_name} option '{option_value}' selected using: {selector}")
                    time.sleep(1)
                    return True
                except:
                    continue
            
            # JavaScript fallback for option selection
            try:
                result = page.evaluate(f"""(optionValue) => {{
                    const items = Array.from(document.querySelectorAll('.cdk-overlay-container div, li, span, [role="option"]'));
                    const target = items.find(el => el.textContent && el.textContent.trim().toLowerCase() === optionValue.toLowerCase());
                    if (target && target.offsetParent !== null) {{
                        target.scrollIntoView();
                        target.click();
                        return true;
                    }}
                    return false;
                }}""", option_value)
                
                if result:
                    logger.info(f"✅ {field_name} option '{option_value}' selected via JavaScript")
                    return True
            except:
                pass
            
            logger.error(f"❌ Failed to select {field_name} option: {option_value}")
            return False
            
        except Exception as e:
            logger.error(f"❌ Dropdown selection failed for {field_name}: {e}")
            return False
    
    def create_activity_from_csv_row(self, page, activity_data, row_index):
        """Create a single activity using CSV row data"""
        try:
            subject = activity_data["subject"]
            logger.info(f"📝 Creating activity {row_index}: {subject[:50]}...")
            
            # Step 1: Click the create button (top right dropdown trigger)
            create_button_selector = 'gs-cs360-header .gs-cs360-headeritem2 button'
            try:
                page.click(create_button_selector)
                time.sleep(3)
                logger.info("🔘 Create dropdown button clicked")
            except:
                logger.error("❌ Failed to click create dropdown button")
                return False
            
            # Step 2: Click "Activity" from dropdown
            activity_selectors = [
                'text=Activity',
                '[role="menuitem"]:has-text("Activity")',
                'li:has-text("Activity")',
                'div:has-text("Activity")'
            ]
            
            if not self.wait_for_element_and_click(page, activity_selectors, "Activity", 10000):
                return False
            
            time.sleep(3)  # Wait for modal to load
            
            # Step 3: Select Activity Type
            activity_type = activity_data["activity_type"]
            if activity_type:
                activity_type_selectors = [
                    'nz-select[placeholder*="Activity Type"]',
                    'gs-activity-type-field nz-select',
                    '[data-testid*="activity-type"] nz-select'
                ]
                
                self.select_dropdown_option(page, activity_type_selectors, activity_type, "Activity Type")
            
            # Step 4: Fill Subject field
            subject_selectors = [
                activity_data.get("subject_selector", '#cdk-overlay-12 > gs-composer > div.gs-timeline-composer.cdk-drag.ng-star-inserted > div.gs-activity-composer > form > div.gs-activity-composersection > div.gs-activity-composerright.ng-star-inserted > gs-fields > div > form > div > div:nth-child(3) > gs-text > nz-form-item > nz-form-control > div > span > input'),
                'input[placeholder*="Subject"]',
                'gs-text input',
                'nz-form-item input[type="text"]'
            ]
            
            self.fill_field_with_fallback(page, subject_selectors, subject, "Subject")
            
            # Step 5: Fill Activity Date
            gainsight_date, gainsight_time = self.convert_date_format(activity_data["activity_date"])
            
            if gainsight_date:
                date_selectors = [
                    '#cdk-overlay-15 > div > date-range-popup > div > div > div > calendar-input > div > div > input',
                    'calendar-input input',
                    '[placeholder*="Date"] input',
                    'nz-date-picker input'
                ]
                
                self.fill_field_with_fallback(page, date_selectors, gainsight_date, "Activity Date")
            
            # Step 6: Fill Time field
            if gainsight_time:
                time_selectors = [
                    '#cdk-overlay-12 > gs-composer > div.gs-timeline-composer.cdk-drag.ng-star-inserted > div.gs-activity-composer > form > div.gs-activity-composersection > div.gs-activity-composerright.ng-star-inserted > gs-fields > div > form > div > div:nth-child(4) > gs-datetime-field > nz-form-item:nth-child(1) > div > nz-form-control.timepicker-error.ng-tns-c56-175.ant-form-item-control-wrapper.ant-col.ng-invalid.ng-untouched.ng-pristine > div > span > nz-time-picker > input',
                    'nz-time-picker input',
                    '[placeholder*="Time"] input'
                ]
                
                self.fill_field_with_fallback(page, time_selectors, gainsight_time, "Time")
            
            # Step 7: Fill Notes/Plain Text
            plain_text = activity_data["plain_text"]
            if plain_text:
                try:
                    page.click('#editor > div')
                    time.sleep(1)
                    page.keyboard.type(plain_text)
                    logger.info("✅ Notes field filled")
                except:
                    # Fallback for notes
                    notes_selectors = [
                        '#editor > div',
                        'textarea[placeholder*="Notes"]',
                        'div[contenteditable="true"]',
                        '.editor textarea'
                    ]
                    
                    self.fill_field_with_fallback(page, notes_selectors, plain_text, "Notes")
            
            # Step 8: Fill Internal Recipients (Author Name)
            author_name = activity_data["author_name"]
            if author_name:
                try:
                    # Click on the "Search Users" input to open the dropdown
                    page.click('input[placeholder="Search Users"]')
                    time.sleep(1)
                    
                    # Wait for the overlay input to appear and type the name
                    page.wait_for_selector('#cdk-overlay-17 input', timeout=5000)
                    page.fill('#cdk-overlay-17 input', author_name.split()[0])  # Use first name
                    time.sleep(2)  # Wait for results
                    
                    # Try to click the user from dropdown (generic approach)
                    try:
                        page.click(f'text={author_name}')
                        logger.info(f"✅ Internal recipient '{author_name}' selected")
                    except:
                        # Try clicking first available option
                        try:
                            page.click('.cdk-overlay-container [role="option"]:first-child')
                            logger.info("✅ First available user selected as internal recipient")
                        except:
                            logger.warning(f"⚠️ Could not select internal recipient: {author_name}")
                            
                except Exception as e:
                    logger.warning(f"⚠️ Internal Recipients field handling failed: {e}")
            
            # Step 9: Select Touchpoint Reason
            touchpoint_reason = activity_data["touchpoint_reason"]
            if touchpoint_reason:
                touchpoint_selectors = [
                    'label:text("Touchpoint Reason") ~ div select',
                    'gs-picklist-field[label*="Touchpoint"] nz-select',
                    '[data-testid*="touchpoint"] nz-select'
                ]
                
                self.select_dropdown_option(page, touchpoint_selectors, touchpoint_reason, "Touchpoint Reason")
            
            # Step 10: Select Flow Type
            flow_type = activity_data["flow_type"]
            if flow_type:
                flow_type_selectors = [
                    '#cdk-overlay-12 > gs-composer > div.gs-timeline-composer.cdk-drag.ng-star-inserted > div.gs-activity-composer > form > div.gs-activity-composersection > div.gs-activity-composerright.ng-star-inserted > gs-fields > div > form > div > div:nth-child(9) > gs-picklist-field > nz-form-item > nz-form-control > div > span > nz-select > div > div',
                    'gs-picklist-field[label*="Flow"] nz-select',
                    '[data-testid*="flow"] nz-select'
                ]
                
                self.select_dropdown_option(page, flow_type_selectors, flow_type, "Flow Type")
            
            # Step 11: Scroll if needed and click "Log Activity"
            try:
                page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                time.sleep(1)
            except:
                pass
            
            log_activity_selectors = [
                'button:has-text("Log Activity")',
                'button:has-text("Save")',
                'button:has-text("Create")',
                'button[type="submit"]'
            ]
            
            if self.wait_for_element_and_click(page, log_activity_selectors, "Log Activity", 15000):
                time.sleep(5)  # Wait for submission
                logger.info(f"✅ Activity '{subject[:30]}...' logged successfully")
                return True
            else:
                logger.error(f"❌ Failed to submit activity: {subject[:30]}...")
                return False
            
        except Exception as e:
            logger.error(f"❌ Activity creation failed for row {row_index}: {e}")
            page.screenshot(path=f"/Users/<USER>/Desktop/wild_weasel_gainsight_migration/activity_error_{row_index}_{int(time.time())}.png")
            return False
    
    def migrate_activities_from_csv(self, page):
        """Migrate all activities from CSV using UI automation"""
        logger.info("🔄 Starting CSV-based UI automation migration...")
        
        successful_count = 0
        failed_count = 0
        
        for i, activity in enumerate(self.activities):
            logger.info(f"📝 Processing activity {i+1}/{len(self.activities)}: {activity['subject'][:50]}...")
            
            try:
                if self.create_activity_from_csv_row(page, activity, i+1):
                    successful_count += 1
                    self.migration_results["successful"].append({
                        "row_number": activity["row_number"],
                        "activity": activity,
                        "method": "CSV_UI_AUTOMATION",
                        "timestamp": datetime.now().isoformat(),
                        "subject": activity["subject"]
                    })
                    logger.info(f"✅ CSV UI Success: {activity['subject'][:50]}")
                else:
                    failed_count += 1
                    self.migration_results["failed"].append({
                        "row_number": activity["row_number"],
                        "activity": activity,
                        "error": "CSV UI automation failed",
                        "timestamp": datetime.now().isoformat(),
                        "subject": activity["subject"]
                    })
                    logger.error(f"❌ CSV UI failed for: {activity['subject'][:50]}")
                
            except Exception as e:
                failed_count += 1
                self.migration_results["failed"].append({
                    "row_number": activity["row_number"],
                    "activity": activity,
                    "error": f"CSV UI exception: {str(e)}",
                    "timestamp": datetime.now().isoformat(),
                    "subject": activity["subject"]
                })
                logger.error(f"❌ CSV UI exception for {activity['subject'][:50]}: {e}")
            
            # Delay between activities
            time.sleep(3)
            
            # Add longer pause every 10 activities to avoid overwhelming the system
            if (i + 1) % 10 == 0:
                logger.info(f"⏸️ Taking a longer break after {i+1} activities...")
                time.sleep(10)
        
        logger.info(f"🔄 CSV UI Migration complete: {successful_count} successful, {failed_count} failed")
        return successful_count, failed_count
    
    def save_results(self):
        """Save migration results"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Save successful migrations
            success_file = self.config["success_log"].replace('.json', f'_{timestamp}.json')
            with open(success_file, 'w') as f:
                json.dump(self.migration_results["successful"], f, indent=2, default=str)
            
            # Save failed migrations
            failed_file = self.config["failed_log"].replace('.json', f'_{timestamp}.json')
            with open(failed_file, 'w') as f:
                json.dump(self.migration_results["failed"], f, indent=2, default=str)
            
            logger.info(f"💾 Results saved: {success_file}, {failed_file}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save results: {e}")
    
    def generate_report(self):
        """Generate final report"""
        total_activities = len(self.activities)
        successful = len(self.migration_results["successful"])
        failed = len(self.migration_results["failed"])
        success_rate = (successful / total_activities * 100) if total_activities > 0 else 0
        
        report = f"""
🐺 WILD WEASEL COMPLETE UI AUTOMATION REPORT
{'='*100}
📊 MIGRATION STATISTICS:
  Total Activities: {total_activities}
  ✅ Successful: {successful}
  ❌ Failed: {failed}
  📈 Success Rate: {success_rate:.1f}%

🔧 METHOD: Complete CSV-Based UI Browser Automation
  → Full CSV field mapping and processing
  → Date format conversion and null value handling
  → Comprehensive form filling with all fields
  → Smart dropdown selection and user search

📋 DATA SOURCE: ICICI Bank CSV Migration
  Source: {self.config['csv_file']}
  Target: Gainsight Timeline Activities via Complete UI Automation
  
📝 FIELDS PROCESSED:
  → Activity Type, Subject, Activity Date & Time
  → Plain Text Notes, Internal Recipients
  → Touchpoint Reason, Flow Type
  → Smart null value handling

🐺 Wild Weasel Complete UI Status: {'✅ MISSION ACCOMPLISHED' if failed == 0 else f'⚠️ PARTIALLY COMPLETED ({successful}/{total_activities} successful)'}
{'='*100}
"""
        
        print(report)
        
        # Save report to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"/Users/<USER>/Desktop/wild_weasel_gainsight_migration/complete_ui_migration_report_{timestamp}.txt"
        try:
            with open(report_file, 'w') as f:
                f.write(report)
            logger.info(f"📄 Report saved to: {report_file}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to save report: {e}")
    
    def execute_complete_ui_mission(self):
        """Execute the complete CSV-based UI automation mission"""
        try:
            self.mission_brief()
            
            # Load CSV activities
            if not self.load_csv_activities():
                logger.error("❌ Mission aborted: Could not load CSV activities")
                return False
            
            self.migration_results["start_time"] = datetime.now()
            self.migration_results["total_processed"] = len(self.activities)
            
            # Execute complete UI automation
            with sync_playwright() as playwright:
                browser = playwright.chromium.launch(headless=False)  # Keep visible for monitoring
                context = browser.new_context(
                    viewport={'width': 1920, 'height': 1080}  # Larger viewport
                )
                page = context.new_page()
                
                # Login
                if not self.login_to_gainsight(page):
                    logger.error("❌ Mission aborted: Login failed")
                    browser.close()
                    return False
                
                # Navigate to timeline
                if not self.navigate_to_timeline(page):
                    logger.error("❌ Mission aborted: Timeline navigation failed")
                    browser.close()
                    return False
                
                # Migrate all CSV activities
                self.migrate_activities_from_csv(page)
                
                # Keep browser open for a moment to see final state
                time.sleep(5)
                browser.close()
            
            self.migration_results["end_time"] = datetime.now()
            
            # Save results and generate report
            self.save_results()
            self.generate_report()
            
            logger.info("🐺 Wild Weasel Complete UI Automation mission accomplished!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Complete UI Mission execution failed: {e}")
            return False

def main():
    """Main execution function"""
    try:
        agent = WildWeaselCompleteUIAutomation()
        agent.execute_complete_ui_mission()
        
    except KeyboardInterrupt:
        print("\n🐺 Wild Weasel Complete UI Automation interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Wild Weasel Complete UI Automation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()